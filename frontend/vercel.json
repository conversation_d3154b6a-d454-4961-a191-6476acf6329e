{"version": 2, "name": "insure-ai", "builds": [{"src": "package.json", "use": "@vercel/next"}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/(.*)", "dest": "/$1"}], "env": {"NEXT_PUBLIC_SUPABASE_URL": "@supabase-url", "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase-anon-key", "SUPABASE_SERVICE_ROLE_KEY": "@supabase-service-role-key", "AWS_ACCESS_KEY_ID": "@aws-access-key-id", "AWS_SECRET_ACCESS_KEY": "@aws-secret-access-key", "AWS_REGION": "@aws-region", "NEXTAUTH_SECRET": "@nextauth-secret", "NEXTAUTH_URL": "@nextauth-url"}, "functions": {"pages/api/**/*.js": {"maxDuration": 30}}}