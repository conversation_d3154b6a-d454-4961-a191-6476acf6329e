import { NextApiRequest, NextApiResponse } from 'next';

interface HealthStatus {
  status: 'healthy' | 'unhealthy';
  timestamp: string;
  version: string;
  environment: string;
  services: {
    database: 'connected' | 'disconnected' | 'unknown';
    aws: 'connected' | 'disconnected' | 'unknown';
  };
  uptime: number;
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse<HealthStatus>
) {
  if (req.method !== 'GET') {
    return res.status(405).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: 'unknown',
        aws: 'unknown',
      },
      uptime: process.uptime(),
    });
  }

  try {
    // Check database connection (Supabase)
    let databaseStatus: 'connected' | 'disconnected' | 'unknown' = 'unknown';
    try {
      if (process.env.NEXT_PUBLIC_SUPABASE_URL) {
        // Simple check - if we have the URL, assume it's configured
        databaseStatus = 'connected';
      } else {
        databaseStatus = 'disconnected';
      }
    } catch (error) {
      databaseStatus = 'disconnected';
    }

    // Check AWS connection
    let awsStatus: 'connected' | 'disconnected' | 'unknown' = 'unknown';
    try {
      if (process.env.AWS_ACCESS_KEY_ID && process.env.AWS_SECRET_ACCESS_KEY) {
        awsStatus = 'connected';
      } else {
        awsStatus = 'disconnected';
      }
    } catch (error) {
      awsStatus = 'disconnected';
    }

    const healthStatus: HealthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: databaseStatus,
        aws: awsStatus,
      },
      uptime: process.uptime(),
    };

    // Set cache headers
    res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');

    res.status(200).json(healthStatus);
  } catch (error) {
    console.error('Health check failed:', error);
    
    const healthStatus: HealthStatus = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      services: {
        database: 'unknown',
        aws: 'unknown',
      },
      uptime: process.uptime(),
    };

    res.status(503).json(healthStatus);
  }
}
