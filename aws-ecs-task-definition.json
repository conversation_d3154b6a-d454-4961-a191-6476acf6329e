{"family": "insure-ai-task", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024", "executionRoleArn": "arn:aws:iam::YOUR_ACCOUNT_ID:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::YOUR_ACCOUNT_ID:role/ecsTaskRole", "containerDefinitions": [{"name": "insure-ai-frontend", "image": "YOUR_ACCOUNT_ID.dkr.ecr.YOUR_REGION.amazonaws.com/insure-ai:latest", "portMappings": [{"containerPort": 3000, "protocol": "tcp"}], "essential": true, "environment": [{"name": "NODE_ENV", "value": "production"}, {"name": "NEXT_PUBLIC_API_URL", "value": "https://your-domain.com"}], "secrets": [{"name": "NEXT_PUBLIC_SUPABASE_URL", "valueFrom": "arn:aws:secretsmanager:YOUR_REGION:YOUR_ACCOUNT_ID:secret:insure-ai/supabase-url"}, {"name": "NEXT_PUBLIC_SUPABASE_ANON_KEY", "valueFrom": "arn:aws:secretsmanager:YOUR_REGION:YOUR_ACCOUNT_ID:secret:insure-ai/supabase-anon-key"}, {"name": "SUPABASE_SERVICE_ROLE_KEY", "valueFrom": "arn:aws:secretsmanager:YOUR_REGION:YOUR_ACCOUNT_ID:secret:insure-ai/supabase-service-role-key"}, {"name": "AWS_ACCESS_KEY_ID", "valueFrom": "arn:aws:secretsmanager:YOUR_REGION:YOUR_ACCOUNT_ID:secret:insure-ai/aws-access-key-id"}, {"name": "AWS_SECRET_ACCESS_KEY", "valueFrom": "arn:aws:secretsmanager:YOUR_REGION:YOUR_ACCOUNT_ID:secret:insure-ai/aws-secret-access-key"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/insure-ai", "awslogs-region": "YOUR_REGION", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 60}}]}