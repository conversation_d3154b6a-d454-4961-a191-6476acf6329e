# InsureAI Deployment Guide

This guide covers multiple deployment options for the InsureAI application.

## 🚀 Quick Start

The easiest way to deploy InsureAI is using our deployment script:

```bash
# Make the script executable
chmod +x deploy.sh

# Set up the project
./deploy.sh setup

# Start development server
./deploy.sh dev

# Deploy to Vercel
./deploy.sh deploy
```

## 📋 Prerequisites

- Node.js 18+ 
- npm or yarn
- Git
- Supabase account
- (Optional) AWS account for Bedrock integration
- (Optional) Vercel account for hosting
- (Optional) Docker for containerized deployment

## 🌐 Deployment Options

### 1. Vercel (Recommended)

Vercel provides the easiest deployment experience for Next.js applications.

#### Setup:
1. Install Vercel CLI: `npm i -g vercel`
2. Login: `vercel login`
3. Deploy: `./deploy.sh deploy`

#### Environment Variables:
Set these in your Vercel dashboard:
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY`
- `AWS_ACCESS_KEY_ID` (optional)
- `AWS_SECRET_ACCESS_KEY` (optional)
- `AWS_REGION` (optional)

### 2. Docker Deployment

#### Build and Run:
```bash
# Build the Docker image
docker build -t insure-ai ./frontend

# Run the container
docker run -p 3000:3000 \
  -e NEXT_PUBLIC_SUPABASE_URL=your_url \
  -e NEXT_PUBLIC_SUPABASE_ANON_KEY=your_key \
  insure-ai
```

#### Docker Compose:
```bash
# Start all services
docker-compose up -d

# With production profile (includes Nginx)
docker-compose --profile production up -d
```

### 3. AWS ECS (Production)

#### Prerequisites:
- AWS CLI configured
- ECR repository created
- ECS cluster set up

#### Deploy:
```bash
# Build and push to ECR
./deploy.sh aws

# Update ECS service with new image
aws ecs update-service \
  --cluster insure-ai-cluster \
  --service insure-ai-service \
  --force-new-deployment
```

#### Terraform Infrastructure:
```bash
cd terraform
terraform init
terraform plan
terraform apply
```

### 4. Manual Deployment

#### Build:
```bash
cd frontend
npm install
npm run build
npm start
```

## 🔧 Configuration

### Environment Variables

#### Required:
- `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key

#### Optional:
- `SUPABASE_SERVICE_ROLE_KEY`: For server-side operations
- `AWS_ACCESS_KEY_ID`: For AWS Bedrock integration
- `AWS_SECRET_ACCESS_KEY`: For AWS Bedrock integration
- `AWS_REGION`: AWS region (default: us-east-1)
- `NEXT_PUBLIC_USE_MOCK_SERVICES`: Use mock services (default: true)

### Supabase Setup

1. Create a new Supabase project
2. Set up authentication (email/password)
3. Create the required tables:
   - `users`
   - `documents`
   - `templates`
   - `form_fields`

### AWS Bedrock Setup (Optional)

1. Enable AWS Bedrock in your AWS account
2. Request access to required models:
   - Claude 3 Sonnet
   - Claude 3 Haiku
3. Create IAM user with Bedrock permissions
4. Configure AWS credentials

## 🔒 Security

### Production Checklist:
- [ ] Set strong `NEXTAUTH_SECRET`
- [ ] Use HTTPS in production
- [ ] Configure CORS properly
- [ ] Set up rate limiting
- [ ] Enable Supabase RLS policies
- [ ] Use AWS IAM roles with minimal permissions
- [ ] Regular security updates

### Environment Security:
- Never commit `.env` files
- Use secrets management (AWS Secrets Manager, Vercel Environment Variables)
- Rotate keys regularly
- Monitor access logs

## 📊 Monitoring

### Health Checks:
- Application: `/api/health`
- Database: Supabase dashboard
- AWS: CloudWatch metrics

### Logging:
- Application logs: Console/CloudWatch
- Access logs: Nginx/ALB
- Error tracking: Sentry (optional)

## 🚨 Troubleshooting

### Common Issues:

#### Build Failures:
```bash
# Clear cache and reinstall
rm -rf .next node_modules
npm install
npm run build
```

#### Environment Variables:
```bash
# Check if variables are loaded
npm run dev
# Check browser console for missing variables
```

#### Docker Issues:
```bash
# Check container logs
docker logs container_name

# Rebuild without cache
docker build --no-cache -t insure-ai ./frontend
```

#### Supabase Connection:
- Verify URL and keys in Supabase dashboard
- Check network connectivity
- Verify RLS policies

## 📞 Support

For deployment issues:
1. Check the logs first
2. Verify environment variables
3. Test locally before deploying
4. Check service status pages (Vercel, Supabase, AWS)

## 🔄 CI/CD

GitHub Actions workflow is included for automated deployment:
- Runs tests on PR
- Deploys to staging on `develop` branch
- Deploys to production on `main` branch

Configure these secrets in GitHub:
- `VERCEL_TOKEN`
- `VERCEL_ORG_ID`
- `VERCEL_PROJECT_ID`
- `DOCKER_USERNAME`
- `DOCKER_PASSWORD`
